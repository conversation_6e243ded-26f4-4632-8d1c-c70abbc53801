<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户信息表单</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/style.css">
</head>
<body>
    <div class="container">
        <h1 class="form-title">用户信息表单</h1>

        <!-- 消息提示区域 -->
        <div id="successMessage" class="success-message"></div>
        <div id="errorMessage" class="error-message"></div>

        <form id="userForm">
            <!-- 姓名 -->
            <div class="form-group">
                <label for="name">姓名 *</label>
                <input type="text" id="name" name="name" class="form-control" placeholder="请输入您的姓名" required>
            </div>

            <!-- 性别 -->
            <div class="form-group">
                <label>性别 *</label>
                <div class="radio-group">
                    <div class="radio-item">
                        <input type="radio" id="male" name="gender" value="男" checked>
                        <label for="male">男</label>
                    </div>
                    <div class="radio-item">
                        <input type="radio" id="female" name="gender" value="女">
                        <label for="female">女</label>
                    </div>
                </div>
            </div>

            <!-- 省份 -->
            <div class="form-group">
                <label for="province">省份 *</label>
                <select id="province" name="province" class="select-control" required>
                    <option value="">请选择省份</option>
                    <option value="北京">北京</option>
                    <option value="上海">上海</option>
                    <option value="广东">广东</option>
                    <option value="浙江">浙江</option>
                    <option value="江苏">江苏</option>
                    <option value="山东">山东</option>
                    <option value="河南">河南</option>
                    <option value="四川">四川</option>
                    <option value="湖北">湖北</option>
                    <option value="湖南">湖南</option>
                </select>
            </div>

            <!-- 熟悉的编程语言 -->
            <div class="form-group">
                <label for="languages">熟悉的编程语言 * (可多选)</label>
                <select id="languages" name="languages" class="select-control" multiple size="5" required>
                    <option value="Java">Java</option>
                    <option value="JavaScript">JavaScript</option>
                    <option value="HTML">HTML</option>
                    <option value="CSS">CSS</option>
                    <option value="Python">Python</option>
                    <option value="SQL">SQL</option>
                    <option value="C++">C++</option>
                    <option value="C#">C#</option>
                    <option value="PHP">PHP</option>
                    <option value="Go">Go</option>
                </select>
                <small style="color: #666; font-size: 12px;">按住 Ctrl 键可多选</small>
            </div>

            <!-- 密码 -->
            <div class="form-group">
                <label for="password">密码 *</label>
                <input type="password" id="password" name="password" class="form-control" placeholder="请输入密码（至少6位）" required>
            </div>

            <!-- 爱好 -->
            <div class="form-group">
                <label>爱好</label>
                <div class="checkbox-group">
                    <div class="checkbox-item">
                        <input type="checkbox" id="movie" name="hobby" value="电影">
                        <label for="movie">电影</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="food" name="hobby" value="美食">
                        <label for="food">美食</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="travel" name="hobby" value="旅游">
                        <label for="travel">旅游</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="reading" name="hobby" value="阅读">
                        <label for="reading">阅读</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="music" name="hobby" value="音乐">
                        <label for="music">音乐</label>
                    </div>
                    <div class="checkbox-item">
                        <input type="checkbox" id="sports" name="hobby" value="运动">
                        <label for="sports">运动</label>
                    </div>
                </div>
            </div>

            <!-- 头像上传 -->
            <div class="form-group">
                <label for="avatar">头像</label>
                <div class="file-input-wrapper">
                    <input type="file" id="avatar" name="avatar" class="file-input" accept="image/*">
                    <label for="avatar" class="file-input-label">点击选择头像文件</label>
                </div>
            </div>

            <!-- 按钮组 -->
            <div class="button-group">
                <button type="button" id="submitBtn" class="btn btn-primary">提交</button>
                <button type="button" id="resetBtn" class="btn btn-secondary">重置</button>
            </div>
        </form>
    </div>

    <script src="${pageContext.request.contextPath}/static/js/script.js"></script>
</body>
</html>