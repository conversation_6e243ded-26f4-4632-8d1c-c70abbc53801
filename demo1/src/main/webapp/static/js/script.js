// DOM加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 获取表单元素
    const form = document.getElementById('userForm');
    const submitBtn = document.getElementById('submitBtn');
    const resetBtn = document.getElementById('resetBtn');
    const fileInput = document.getElementById('avatar');
    const fileLabel = document.querySelector('.file-input-label');
    const successMessage = document.getElementById('successMessage');
    const errorMessage = document.getElementById('errorMessage');

    // 文件上传处理
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            fileLabel.textContent = `已选择: ${file.name}`;
            fileLabel.style.color = '#667eea';
        } else {
            fileLabel.textContent = '点击选择头像文件';
            fileLabel.style.color = '#666';
        }
    });

    // 表单验证函数
    function validateForm() {
        const name = document.getElementById('name').value.trim();
        const password = document.getElementById('password').value;
        const province = document.getElementById('province').value;
        const languages = document.getElementById('languages').selectedOptions;
        
        // 清除之前的错误消息
        hideMessages();
        
        // 验证姓名
        if (!name) {
            showError('请输入姓名');
            return false;
        }
        
        if (name.length < 2) {
            showError('姓名至少需要2个字符');
            return false;
        }
        
        // 验证密码
        if (!password) {
            showError('请输入密码');
            return false;
        }
        
        if (password.length < 6) {
            showError('密码至少需要6个字符');
            return false;
        }
        
        // 验证省份
        if (!province) {
            showError('请选择省份');
            return false;
        }
        
        // 验证编程语言
        if (languages.length === 0) {
            showError('请至少选择一种熟悉的编程语言');
            return false;
        }
        
        return true;
    }

    // 显示成功消息
    function showSuccess(message) {
        successMessage.textContent = message;
        successMessage.style.display = 'block';
        errorMessage.style.display = 'none';
        
        // 3秒后自动隐藏
        setTimeout(() => {
            successMessage.style.display = 'none';
        }, 3000);
    }

    // 显示错误消息
    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.style.display = 'block';
        successMessage.style.display = 'none';
        
        // 5秒后自动隐藏
        setTimeout(() => {
            errorMessage.style.display = 'none';
        }, 5000);
    }

    // 隐藏所有消息
    function hideMessages() {
        successMessage.style.display = 'none';
        errorMessage.style.display = 'none';
    }

    // 收集表单数据
    function collectFormData() {
        const formData = {
            name: document.getElementById('name').value.trim(),
            gender: document.querySelector('input[name="gender"]:checked')?.value || '',
            province: document.getElementById('province').value,
            languages: Array.from(document.getElementById('languages').selectedOptions).map(option => option.value),
            password: document.getElementById('password').value,
            hobbies: Array.from(document.querySelectorAll('input[name="hobby"]:checked')).map(checkbox => checkbox.value),
            avatar: fileInput.files[0]?.name || ''
        };
        
        return formData;
    }

    // 提交按钮点击事件
    submitBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        if (validateForm()) {
            const formData = collectFormData();
            
            // 模拟提交过程
            submitBtn.disabled = true;
            submitBtn.textContent = '提交中...';
            
            // 模拟网络请求延迟
            setTimeout(() => {
                console.log('表单数据:', formData);
                showSuccess('表单提交成功！');
                
                // 重置按钮状态
                submitBtn.disabled = false;
                submitBtn.textContent = '提交';
                
                // 可以在这里添加实际的表单提交逻辑
                // 例如：发送AJAX请求到服务器
                
            }, 1500);
        }
    });

    // 重置按钮点击事件
    resetBtn.addEventListener('click', function(e) {
        e.preventDefault();
        
        // 确认重置
        if (confirm('确定要重置表单吗？')) {
            form.reset();
            fileLabel.textContent = '点击选择头像文件';
            fileLabel.style.color = '#666';
            hideMessages();
            showSuccess('表单已重置');
        }
    });

    // 实时输入验证
    document.getElementById('name').addEventListener('input', function() {
        const name = this.value.trim();
        if (name && name.length >= 2) {
            this.style.borderColor = '#28a745';
        } else {
            this.style.borderColor = '#e1e5e9';
        }
    });

    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        if (password && password.length >= 6) {
            this.style.borderColor = '#28a745';
        } else {
            this.style.borderColor = '#e1e5e9';
        }
    });

    // 添加输入框焦点效果
    const inputs = document.querySelectorAll('.form-control, .select-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });

    // 页面加载动画
    setTimeout(() => {
        document.querySelector('.container').style.opacity = '1';
        document.querySelector('.container').style.transform = 'translateY(0)';
    }, 100);
});

// 工具函数：格式化日期
function formatDate(date) {
    return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// 工具函数：生成随机ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}
